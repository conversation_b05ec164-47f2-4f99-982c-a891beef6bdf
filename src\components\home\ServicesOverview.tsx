"use client";

import React from 'react';
import { motion } from 'motion/react';
import { Zap, Wind, Droplets, Shield, Building2, Cog } from 'lucide-react';
import Image from 'next/image';

const services = [
  {
    icon: Zap,
    title: "Electrical Engineering",
    description: "Complete electrical system design, power distribution, lighting design, and emergency systems for commercial and industrial projects.",
    image: "/images/services/electrical.jpg",
    features: ["Power Distribution", "Lighting Design", "Emergency Systems", "Code Compliance"]
  },
  {
    icon: Wind,
    title: "HVAC Systems",
    description: "Energy-efficient heating, ventilation, and air conditioning solutions tailored to your building's specific requirements.",
    image: "/images/services/hvac.jpg",
    features: ["Energy Efficiency", "Climate Control", "Air Quality", "System Optimization"]
  },
  {
    icon: Droplets,
    title: "Plumbing Engineering",
    description: "Comprehensive plumbing system design including water supply, drainage, fire protection, and specialized piping systems.",
    image: "/images/services/plumbing.jpg",
    features: ["Water Systems", "Fire Protection", "Drainage Design", "Specialized Piping"]
  },
  {
    icon: Shield,
    title: "Fire Protection",
    description: "Advanced fire protection systems design ensuring safety compliance and optimal protection for your facility.",
    image: "/images/services/mechanical.jpg",
    features: ["Sprinkler Systems", "Fire Alarms", "Smoke Control", "Safety Compliance"]
  },
  {
    icon: Building2,
    title: "Building Automation",
    description: "Smart building solutions integrating all MEP systems for optimal performance, efficiency, and user comfort.",
    image: "/images/services/electrical.jpg",
    features: ["Smart Controls", "Energy Management", "System Integration", "Remote Monitoring"]
  },
  {
    icon: Cog,
    title: "Commissioning",
    description: "Comprehensive testing and commissioning services to ensure all systems operate as designed and meet performance criteria.",
    image: "/images/services/hvac.jpg",
    features: ["System Testing", "Performance Verification", "Documentation", "Training"]
  }
];

const ServicesOverview = () => {
  return (
    <section className="py-20 bg-gradient-to-b from-slate-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-4"
          >
            <Cog className="w-4 h-4 mr-2" />
            Our Core Services
          </motion.div>
          
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Comprehensive MEP Engineering Solutions
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From initial concept to final commissioning, we deliver integrated mechanical, electrical, and plumbing engineering services that exceed industry standards.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100">
                {/* Service Image */}
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={service.image}
                    alt={service.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  
                  {/* Icon */}
                  <div className="absolute top-4 left-4">
                    <div className="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-lg flex items-center justify-center">
                      <service.icon className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                    {service.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Features */}
                  <div className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center text-sm text-gray-500">
                        <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2"></div>
                        {feature}
                      </div>
                    ))}
                  </div>

                  {/* Learn More Link */}
                  <motion.div
                    className="mt-4 pt-4 border-t border-gray-100"
                    whileHover={{ x: 5 }}
                  >
                    <span className="text-blue-600 font-medium text-sm cursor-pointer hover:text-blue-700 transition-colors">
                      Learn More →
                    </span>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-8 lg:p-12">
            <h3 className="text-2xl lg:text-3xl font-bold text-white mb-4">
              Ready to Start Your Project?
            </h3>
            <p className="text-blue-100 mb-8 max-w-2xl mx-auto">
              Let our experienced team of engineers help you bring your vision to life with innovative MEP solutions.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Get Free Consultation
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesOverview;
