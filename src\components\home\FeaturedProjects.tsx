"use client";

import React from 'react';
import { motion } from 'motion/react';
import { ArrowRight, MapPin, Calendar, Users, Award } from 'lucide-react';
import Image from 'next/image';

const featuredProjects = [
  {
    id: 1,
    title: "Corporate Headquarters Complex",
    category: "Commercial",
    location: "Downtown Business District",
    year: "2024",
    image: "/images/projects/project-1.jpg",
    description: "Complete MEP engineering for a 25-story corporate headquarters featuring advanced building automation, energy-efficient HVAC systems, and state-of-the-art electrical infrastructure.",
    highlights: ["LEED Platinum Certified", "Smart Building Technology", "Energy Savings: 40%", "25 Floors"],
    value: "$2.5M"
  },
  {
    id: 2,
    title: "Medical Research Facility",
    category: "Healthcare",
    location: "Medical Center",
    year: "2023",
    image: "/images/projects/project-2.jpg",
    description: "Specialized MEP systems for a cutting-edge medical research facility including cleanroom environments, precision climate control, and redundant power systems.",
    highlights: ["ISO Class 5 Cleanrooms", "Redundant Systems", "24/7 Operations", "Research Labs"],
    value: "$3.2M"
  },
  {
    id: 3,
    title: "Manufacturing Plant Expansion",
    category: "Industrial",
    location: "Industrial Park",
    year: "2023",
    image: "/images/projects/project-3.jpg",
    description: "Large-scale industrial MEP engineering for manufacturing plant expansion including high-voltage electrical systems, process cooling, and specialized ventilation.",
    highlights: ["High-Voltage Systems", "Process Integration", "Safety Compliance", "Expansion Ready"],
    value: "$4.1M"
  }
];

const stats = [
  { number: "500+", label: "Projects Completed", icon: Award },
  { number: "15+", label: "Years Experience", icon: Calendar },
  { number: "25+", label: "Expert Engineers", icon: Users },
  { number: "98%", label: "Client Satisfaction", icon: Award }
];

const FeaturedProjects = () => {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium mb-4"
          >
            <Award className="w-4 h-4 mr-2" />
            Featured Projects
          </motion.div>
          
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Engineering Excellence in Action
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover how we've transformed complex engineering challenges into successful realities across diverse industries and project scales.
          </p>
        </motion.div>

        {/* Projects Grid */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {featuredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="group cursor-pointer"
            >
              <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-gray-100">
                {/* Project Image */}
                <div className="relative h-64 overflow-hidden">
                  <Image
                    src={project.image}
                    alt={project.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                  
                  {/* Category Badge */}
                  <div className="absolute top-4 left-4">
                    <span className="px-3 py-1 bg-white/90 backdrop-blur-sm text-gray-800 text-sm font-medium rounded-full">
                      {project.category}
                    </span>
                  </div>

                  {/* Project Value */}
                  <div className="absolute top-4 right-4">
                    <span className="px-3 py-1 bg-blue-600/90 backdrop-blur-sm text-white text-sm font-medium rounded-full">
                      {project.value}
                    </span>
                  </div>

                  {/* Project Info Overlay */}
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center text-white text-sm space-x-4">
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        {project.location}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        {project.year}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                    {project.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {project.description}
                  </p>

                  {/* Highlights */}
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    {project.highlights.map((highlight, highlightIndex) => (
                      <div key={highlightIndex} className="flex items-center text-sm text-gray-500">
                        <div className="w-1.5 h-1.5 bg-green-600 rounded-full mr-2"></div>
                        {highlight}
                      </div>
                    ))}
                  </div>

                  {/* View Project Link */}
                  <motion.div
                    className="flex items-center justify-between pt-4 border-t border-gray-100"
                    whileHover={{ x: 5 }}
                  >
                    <span className="text-blue-600 font-medium text-sm group-hover:text-blue-700 transition-colors">
                      View Project Details
                    </span>
                    <ArrowRight className="w-4 h-4 text-blue-600 group-hover:text-blue-700 transition-colors" />
                  </motion.div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-slate-900 to-slate-800 rounded-2xl p-8 lg:p-12"
        >
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="w-8 h-8 text-blue-400" />
                </div>
                <div className="text-3xl lg:text-4xl font-bold text-white mb-2">{stat.number}</div>
                <div className="text-gray-300">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* View All Projects CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg shadow-lg hover:bg-blue-700 transition-all duration-300"
          >
            View All Projects
            <ArrowRight className="ml-2 w-5 h-5" />
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturedProjects;
