"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { Menu, Grid3X3, Zap, Settings, Droplets, Shield } from "lucide-react";
import { motion } from "motion/react";

import {
  Sheet,
  SheetContent,
  SheetTrigger,
  SheetClose,
} from "@/components/ui/sheet";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import JsConsultants from "../intro/JsConsultants";
import { SERVICES, NAVIGATION_ITEMS } from "@/utils/constants";

const iconMap = {
  Zap,
  Settings,
  Droplets,
  Shield
};

const services = SERVICES.map(service => ({
  title: service.title,
  description: service.shortDescription,
  image: `/images/services/${service.title.toLowerCase().replace(/\s+/g, '-')}.jpg`,
  href: "/services",
  icon: service.icon
}));

export default function Header() {
  const [hoveredService, setHoveredService] = useState(services[0]);
  const [isOpen, setIsOpen] = useState(false);

  return (
    <nav className="border-b border-gray-400 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <motion.div
            className="border border-y-0 px-3 border-gray-400 h-full flex justify-center items-center"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <Link href="/" className="flex items-center space-x-2">
              <JsConsultants className="w-9 h-9" />
              <span className="text-xl font-semibold text-gray-800 font-space-mono">
                Consultants
              </span>
            </Link>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="bg-transparent hover:to-blue-primary data-[state=open]:bg-blue-200">
                    <Grid3X3 className="w-4 h-4 mr-2" />
                    Services
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="w-full max-w-4xl p-6">
                      <div className="flex gap-6 w-[800px]">
                        {/* Left side - Image */}
                        <div className="flex-shrink-0 w-1/3">
                          <div className="bg-gray-100 rounded-lg overflow-hidden">
                            <Image
                              src={hoveredService.image || "/placeholder.svg"}
                              alt={hoveredService.title}
                              width={320}
                              height={200}
                              className="w-full h-48 object-cover"
                            />
                            <div className="p-4">
                              <h3 className="font-semibold text-lg text-gray-900">
                                {hoveredService.title}
                              </h3>
                              <p className="text-gray-600 text-sm mt-2 leading-relaxed">
                                {hoveredService.description}
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Right side - Menu items */}
                        <div className="flex-1 min-w-0">
                          <div className="grid grid-cols-2 gap-3">
                            {services.map((service, index) => (
                              <NavigationMenuLink
                                key={index}
                                asChild
                                onMouseEnter={() => setHoveredService(service)}
                              >
                                <Link
                                  href={service.href}
                                  className="block p-4 rounded-lg hover:bg-blue-50 transition-colors border border-transparent hover:border-blue-200 group"
                                >
                                  <div className="font-medium text-gray-900 group-hover:text-blue-700 transition-colors">
                                    {service.title}
                                  </div>
                                  <div className="text-sm text-gray-600 mt-1 line-clamp-2">
                                    {service.description}
                                  </div>
                                </Link>
                              </NavigationMenuLink>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>

            {NAVIGATION_ITEMS.filter(item => item.name !== 'Services').map((item) => (
              <motion.div
                key={item.name}
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <Link
                  href={item.href}
                  className="text-gray-700 hover:text-blue-900 font-medium transition-colors"
                >
                  {item.name}
                </Link>
              </motion.div>
            ))}
          </div>

          {/* Enquire Now Button - Desktop */}
          <div className="hidden lg:flex border border-y-0 border-gray-400 h-full justify-center items-center p-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="text-black font-medium hover:text-blue-900 transition-colors"
            >
              Enquire now
            </motion.button>
          </div>

          {/* Mobile Menu Button */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild className="lg:hidden">
              <button>
                <Menu className="h-6 w-6" />
                <span className="sr-only">Open menu</span>
              </button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col space-y-4 mt-8">
                {NAVIGATION_ITEMS.map((item) => (
                  item.name === 'Services' ? (
                    <div key={item.name} className="space-y-2">
                      <div className="text-lg font-medium text-gray-900 flex items-center">
                        <Grid3X3 className="w-4 h-4 mr-2" />
                        Services
                      </div>
                      <div className="pl-6 space-y-2">
                        {services.map((service, index) => (
                          <SheetClose key={index} asChild>
                            <Link
                              href={service.href}
                              className="block text-gray-600 hover:text-blue-600 transition-colors"
                            >
                              {service.title}
                            </Link>
                          </SheetClose>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <SheetClose key={item.name} asChild>
                      <Link
                        href={item.href}
                        className="text-lg font-medium hover:text-blue-600 transition-colors"
                      >
                        {item.name}
                      </Link>
                    </SheetClose>
                  )
                ))}

                <div className="pt-4 border-t border-gray-200">
                  <SheetClose asChild>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                    >
                      Enquire now
                    </motion.button>
                  </SheetClose>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  );
}
