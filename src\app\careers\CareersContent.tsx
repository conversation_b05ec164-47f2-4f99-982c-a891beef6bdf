'use client'

import { motion } from 'motion/react'
import { ScrollReveal } from '@/components/ui/ScrollReveal'
import { JobCard } from '@/components/careers/JobCard'
import { TeamCulture } from '@/components/careers/TeamCulture'
import { ApplicationForm } from '@/components/careers/ApplicationForm'

const jobOpenings = [
  {
    id: 1,
    title: "Senior Electrical Engineer",
    department: "Electrical Engineering",
    location: "Chennai, Tamil Nadu",
    type: "Full-time",
    experience: "5-8 years",
    description: "Lead electrical system design for commercial and industrial projects. Expertise in lighting design, power distribution, and energy-efficient solutions required.",
    requirements: [
      "Bachelor's degree in Electrical Engineering",
      "5+ years of MEP consulting experience",
      "Proficiency in AutoCAD, Revit, and electrical design software",
      "Knowledge of electrical codes and standards",
      "Strong project management skills"
    ]
  },
  {
    id: 2,
    title: "Mechanical Engineer",
    department: "Mechanical Engineering",
    location: "Chennai, Tamil Nadu",
    type: "Full-time",
    experience: "3-5 years",
    description: "Design HVAC systems and mechanical solutions for various building types. Focus on energy-efficient and sustainable mechanical systems.",
    requirements: [
      "Bachelor's degree in Mechanical Engineering",
      "3+ years of HVAC design experience",
      "Knowledge of mechanical codes and energy standards",
      "Experience with mechanical design software",
      "Understanding of sustainable building practices"
    ]
  },
  {
    id: 3,
    title: "Plumbing Engineer",
    department: "Plumbing Engineering",
    location: "Chennai, Tamil Nadu",
    type: "Full-time",
    experience: "2-4 years",
    description: "Design plumbing systems including water supply, drainage, and specialized systems. Work on sustainable plumbing solutions and water conservation.",
    requirements: [
      "Bachelor's degree in Civil/Mechanical Engineering",
      "2+ years of plumbing design experience",
      "Knowledge of plumbing codes and standards",
      "Experience with plumbing design software",
      "Understanding of water conservation principles"
    ]
  },
  {
    id: 4,
    title: "Fire Safety Engineer",
    department: "Fire Safety",
    location: "Chennai, Tamil Nadu",
    type: "Full-time",
    experience: "3-6 years",
    description: "Design fire protection systems including sprinklers, fire hydrants, and alarm systems. Ensure compliance with fire safety codes and regulations.",
    requirements: [
      "Bachelor's degree in Fire Safety/Mechanical Engineering",
      "3+ years of fire protection design experience",
      "Knowledge of fire safety codes and NFPA standards",
      "Experience with fire protection design software",
      "Strong understanding of building safety systems"
    ]
  }
]

export default function CareersContent() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-slate-800">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center text-white"
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Join Our <span className="text-yellow-400">Team</span>
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
                Build your career with 19+ years of MEP engineering excellence
              </p>
            </motion.div>
          </ScrollReveal>
        </div>
      </section>

      {/* Team Culture Section */}
      <TeamCulture />

      {/* Current Openings */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
                Current Openings
              </h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                Explore exciting career opportunities in MEP engineering
              </p>
            </div>
          </ScrollReveal>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {jobOpenings.map((job, index) => (
              <ScrollReveal key={job.id} delay={index * 0.1}>
                <JobCard job={job} />
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Application Form Section */}
      <section className="py-16 bg-slate-100">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
                Apply Now
              </h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                Submit your application and join our team of engineering professionals
              </p>
            </div>
          </ScrollReveal>

          <div className="max-w-4xl mx-auto">
            <ApplicationForm />
          </div>
        </div>
      </section>
    </div>
  )
}
