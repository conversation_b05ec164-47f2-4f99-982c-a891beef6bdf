"use client";

import React from 'react';
import { motion } from 'motion/react';
import { Shield, Award, Users, Zap, CheckCircle, Star } from 'lucide-react';
import Image from 'next/image';

const highlights = [
  {
    icon: Shield,
    title: "Licensed & Certified",
    description: "All our engineers are licensed professionals with industry certifications and continuous education.",
    features: ["PE Licensed Engineers", "LEED Accredited", "Industry Certifications", "Continuous Training"]
  },
  {
    icon: Award,
    title: "Award-Winning Projects",
    description: "Recognition for excellence in engineering design and sustainable building practices.",
    features: ["Engineering Excellence Awards", "LEED Platinum Projects", "Industry Recognition", "Client Testimonials"]
  },
  {
    icon: Users,
    title: "Expert Team",
    description: "Experienced professionals with diverse expertise across all MEP engineering disciplines.",
    features: ["25+ Expert Engineers", "15+ Years Experience", "Diverse Specializations", "Collaborative Approach"]
  },
  {
    icon: Zap,
    title: "Innovation Focus",
    description: "Cutting-edge solutions using the latest technology and sustainable engineering practices.",
    features: ["Smart Building Tech", "Energy Efficiency", "Sustainable Design", "Future-Ready Solutions"]
  }
];

const certifications = [
  { name: "LEED Accredited Professional", abbr: "LEED AP" },
  { name: "Professional Engineer", abbr: "PE" },
  { name: "Certified Energy Manager", abbr: "CEM" },
  { name: "Building Commissioning Professional", abbr: "BCP" }
];

const CompanyHighlights = () => {
  return (
    <section className="py-20 bg-gradient-to-b from-white to-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-4 py-2 bg-amber-100 text-amber-800 rounded-full text-sm font-medium mb-4"
          >
            <Star className="w-4 h-4 mr-2" />
            Why Choose JS Consultants
          </motion.div>
          
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Engineering Excellence You Can Trust
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            With over 15 years of experience and a commitment to innovation, we deliver MEP engineering solutions that exceed expectations and stand the test of time.
          </p>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left Column - Image */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="/images/about/office-interior.jpg"
                alt="JS Consultants Engineering Team"
                width={600}
                height={400}
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-slate-900/40 to-transparent"></div>
              
              {/* Floating Achievement Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
                className="absolute -bottom-6 -right-6 bg-white rounded-xl p-6 shadow-lg border border-gray-100"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Award className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900">98%</div>
                    <div className="text-sm text-gray-600">Client Satisfaction</div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Column - Highlights */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {highlights.map((highlight, index) => (
              <motion.div
                key={highlight.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex space-x-4"
              >
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <highlight.icon className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{highlight.title}</h3>
                  <p className="text-gray-600 mb-3">{highlight.description}</p>
                  <div className="grid grid-cols-2 gap-2">
                    {highlight.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center text-sm text-gray-500">
                        <CheckCircle className="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Certifications Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white rounded-2xl p-8 lg:p-12 shadow-lg border border-gray-100"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
              Professional Certifications & Credentials
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our team maintains the highest professional standards with industry-leading certifications and continuous education.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {certifications.map((cert, index) => (
              <motion.div
                key={cert.abbr}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center p-6 bg-gradient-to-b from-slate-50 to-white rounded-xl border border-gray-100"
              >
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold text-lg">{cert.abbr}</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">{cert.name}</h4>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-slate-900 to-slate-800 rounded-2xl p-8 lg:p-12">
            <h3 className="text-2xl lg:text-3xl font-bold text-white mb-4">
              Experience the JS Consultants Difference
            </h3>
            <p className="text-slate-300 mb-8 max-w-2xl mx-auto">
              Partner with a team that combines technical expertise, innovative solutions, and unwavering commitment to your project's success.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg shadow-lg hover:bg-blue-700 transition-all duration-300"
              >
                Schedule Consultation
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-lg hover:border-blue-400 hover:text-blue-400 transition-all duration-300"
              >
                Learn About Our Team
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CompanyHighlights;
