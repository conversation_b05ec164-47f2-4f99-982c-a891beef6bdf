// Company Information
export const COMPANY_INFO = {
  name: "JS Consultants",
  fullName: "JS Consultants MEP Engineering Services",
  tagline: "19+ Years of MEP Engineering Excellence",
  established: "2005",
  experience: "19+ years",
  description: "A renowned design firm that began as an Electrical Consultant in 2005 and has since evolved into a leading MEP consultancy.",
  mission: "JS Consultants started as a conventional MEP consulting company in India, growing with experience and providing consultant quality and commitment to projects across Southern states and nationwide.",
  coverage: "Southern states and nationwide"
}

// Hero Section Content
export const HERO_CONTENT = {
  primaryHeadline: "We craft energy-efficient lighting designs that elevate experiences, save costs, and delight",
  secondaryHeadline: "19+ Years of MEP Engineering Excellence",
  companyTagline: "From concept to completion, JS Consultants delivers innovative MEP solutions"
}

// Services Content
export const SERVICES = [
  {
    id: 1,
    title: "Electrical Engineering",
    shortDescription: "We craft energy-efficient lighting designs that elevate experiences, save costs, and delight. Expertise meets imagination for stunning, practical illumination.",
    fullDescription: "Comprehensive electrical engineering services including lighting design, power distribution systems, building automation, energy audits, and electrical safety compliance.",
    features: [
      "Lighting design and energy-efficient solutions",
      "Power distribution systems",
      "Building automation and controls",
      "Energy audits and optimization",
      "Electrical safety compliance"
    ],
    icon: "Zap",
    color: "blue"
  },
  {
    id: 2,
    title: "Mechanical Engineering",
    shortDescription: "We power the future with innovative, energy-efficient building designs. Optimizing HVAC and plumbing for maximum savings and sustainability.",
    fullDescription: "Advanced mechanical engineering solutions focusing on HVAC systems, ventilation, energy efficiency, and sustainable building practices.",
    features: [
      "HVAC system design and optimization",
      "Ventilation and air quality systems",
      "Energy-efficient building designs",
      "Thermal comfort analysis",
      "Sustainable mechanical solutions"
    ],
    icon: "Settings",
    color: "green"
  },
  {
    id: 3,
    title: "Plumbing Engineering",
    shortDescription: "We plumb the depths of engineering for efficiency, sustainability, and meticulous compliance. Pre-design to pipes, plans to perfection.",
    fullDescription: "Complete plumbing engineering services including water supply systems, drainage, water purification, and sustainable plumbing solutions.",
    features: [
      "Water supply and distribution systems",
      "Drainage and sewerage systems",
      "Water purification systems",
      "Rainwater harvesting",
      "Sustainable plumbing solutions"
    ],
    icon: "Droplets",
    color: "orange"
  },
  {
    id: 4,
    title: "Fire Safety & Protection",
    shortDescription: "We specialize in designing fire and safety systems to protect your property and ensure safety. Our services include advanced fire sprinkler systems, fire hydrant systems, smoke detection and alarm systems.",
    fullDescription: "Comprehensive fire safety and protection services ensuring building safety and regulatory compliance.",
    features: [
      "Fire sprinkler system design",
      "Fire hydrant systems",
      "Smoke detection and alarm systems",
      "Fire suppression systems",
      "Emergency evacuation planning"
    ],
    icon: "Shield",
    color: "red"
  }
]

// Client Testimonials (Actual Data)
export const TESTIMONIALS = [
  {
    id: 1,
    company: "Gnanam School of Business and Hotel Gnanam",
    location: "Thanjavur",
    testimonial: "We, the Gnanam School of Business and Hotel Gnanam, Thanjavur have a long association with more Than 14+ years with JS Consultants. The organization stands totally committed for delivering quality output, Seamless service and value to their customers.",
    duration: "14+ years association",
    category: "Educational & Hospitality",
    logo: "/images/clients/gnanam-logo.png",
    rating: 5
  },
  {
    id: 2,
    company: "KC Textiles",
    location: "Thiruchendur",
    testimonial: "During our first association, we felt the charges are expensive about. But, the electrical systems are designed very comfortably and also energy-efficiently. So, we are enjoying every moment of every day of the service that has been provided by the JS Team.",
    duration: "Long-term partnership",
    category: "Textile Industry",
    logo: "/images/clients/kc-textiles-logo.png",
    rating: 5
  },
  {
    id: 3,
    company: "Pothys",
    location: "Multiple Locations",
    testimonial: "JS Consultants headed by Mr. Nagarajan have associated with Pothys for the past few years as our electrical consultants. They are energetic and enthusiastic in what they do with innovative ideas.",
    duration: "Multi-year partnership",
    category: "Retail",
    logo: "/images/clients/pothys-logo.png",
    rating: 5
  },
  {
    id: 4,
    company: "Textile Industry Client",
    location: "Tamil Nadu",
    testimonial: "60 years of the textile industry, third generation... JS Sir in particular paid close attention and offered some creative suggestions, both generally and in terms of work. JS Sir has proved his service in lighting design.",
    duration: "Ongoing collaboration",
    category: "Textile Manufacturing",
    logo: "/images/clients/textile-client-logo.png",
    rating: 5
  }
]

// Notable Projects
export const PROJECTS = [
  {
    id: 1,
    title: "Gnanam School of Business and Hotel Gnanam",
    location: "Thanjavur",
    category: "Educational & Hospitality",
    description: "Comprehensive MEP engineering services for educational and hospitality facilities with 14+ years of ongoing partnership.",
    services: ["Electrical", "Mechanical", "Plumbing"],
    image: "/images/projects/gnanam-project.jpg",
    year: "2010-Present",
    status: "Ongoing"
  },
  {
    id: 2,
    title: "KC Textiles",
    location: "Thiruchendur",
    category: "Industrial",
    description: "Energy-efficient electrical systems design for textile manufacturing facility with focus on comfort and efficiency.",
    services: ["Electrical", "Lighting Design"],
    image: "/images/projects/kc-textiles-project.jpg",
    year: "2018",
    status: "Completed"
  },
  {
    id: 3,
    title: "Pothys Retail Showrooms",
    location: "Multiple Locations",
    category: "Commercial",
    description: "Electrical consulting services for multiple retail showroom locations with innovative lighting solutions.",
    services: ["Electrical", "Lighting Design"],
    image: "/images/projects/pothys-project.jpg",
    year: "2019-Present",
    status: "Ongoing"
  },
  {
    id: 4,
    title: "Textile Industry Projects",
    location: "Tamil Nadu",
    category: "Industrial",
    description: "Various textile industry projects with specialized lighting design and electrical systems.",
    services: ["Electrical", "Lighting Design", "Power Systems"],
    image: "/images/projects/textile-industry-project.jpg",
    year: "2015-Present",
    status: "Multiple Projects"
  }
]

// Team Information
export const TEAM_MEMBERS = [
  {
    id: 1,
    name: "Mr. Nagarajan",
    position: "Founder & Principal Consultant",
    department: "Electrical Engineering",
    experience: "19+ years",
    specializations: ["Lighting Design", "Power Systems", "Energy Efficiency", "MEP Consulting"],
    bio: "Founder of JS Consultants with extensive experience in electrical engineering and MEP consulting. Known for innovative lighting design solutions and energy-efficient systems.",
    image: "/images/team/nagarajan.jpg",
    achievements: [
      "Founded JS Consultants in 2005",
      "Led 100+ successful MEP projects",
      "Specialized in energy-efficient lighting design",
      "Established long-term partnerships with major clients"
    ]
  }
]

// Company Culture Values
export const CULTURE_VALUES = [
  {
    title: "Amazing Team Feel",
    description: "We all pull together and help each other when needed. Our collaborative environment fosters growth and innovation.",
    icon: "Users"
  },
  {
    title: "Quality Commitment",
    description: "Delivering quality output, seamless service and value to our customers with 19+ years of experience.",
    icon: "Award"
  },
  {
    title: "Innovation & Energy",
    description: "Energetic and enthusiastic approach with innovative ideas for every project challenge.",
    icon: "Lightbulb"
  },
  {
    title: "Long-term Partnerships",
    description: "Building lasting relationships with clients through consistent quality and reliable service delivery.",
    icon: "Handshake"
  }
]

// Contact Information
export const CONTACT_INFO = {
  address: "Chennai, Tamil Nadu, India",
  phone: "+91 XXX XXX XXXX",
  email: "<EMAIL>",
  website: "www.jsconsultants.com",
  businessHours: "Monday - Friday: 9:00 AM - 6:00 PM",
  responseTime: "We respond within 24 hours"
}

// Navigation Menu Items
export const NAVIGATION_ITEMS = [
  { name: "Home", href: "/" },
  { name: "About", href: "/about" },
  { name: "Services", href: "/services" },
  { name: "Projects", href: "/projects" },
  { name: "Testimonials", href: "/testimonials" },
  { name: "Careers", href: "/careers" },
  { name: "Contact", href: "/contact" }
]

// Call-to-Action Buttons
export const CTA_BUTTONS = [
  { text: "Request Consultation", href: "/contact", primary: true },
  { text: "View Portfolio", href: "/projects", primary: false },
  { text: "Contact Us", href: "/contact", primary: false }
]
