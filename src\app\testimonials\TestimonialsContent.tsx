'use client'

import { motion } from 'motion/react'

import { TestimonialCard } from '@/components/testimonials/TestimonialCard'
import { TestimonialCarousel } from '@/components/testimonials/TestimonialCarousel';
import ScrollReveal from '@/components/ui/ScrollReveal';


const testimonials = [
  {
    id: 1,
    company: "Gnanam School of Business and Hotel Gnanam",
    location: "Thanjavur",
    testimonial: "We, the Gnanam School of Business and Hotel Gnanam, Thanjavur have a long association with more Than 14+ years with JS Consultants. The organization stands totally committed for delivering quality output, Seamless service and value to their customers.",
    duration: "14+ years association",
    category: "Educational & Hospitality",
    logo: "/images/clients/gnanam-logo.png"
  },
  {
    id: 2,
    company: "KC Textiles",
    location: "Thiruchendur",
    testimonial: "During our first association, we felt the charges are expensive about. But, the electrical systems are designed very comfortably and also energy-efficiently. So, we are enjoying every moment of every day of the service that has been provided by the JS Team.",
    duration: "Long-term partnership",
    category: "Textile Industry",
    logo: "/images/clients/kc-textiles-logo.png"
  },
  {
    id: 3,
    company: "Pothys",
    location: "Multiple Locations",
    testimonial: "JS Consultants headed by Mr. <PERSON>ajan have associated with Pothys for the past few years as our electrical consultants. They are energetic and enthusiastic in what they do with innovative ideas.",
    duration: "Multi-year partnership",
    category: "Retail",
    logo: "/images/clients/pothys-logo.png"
  },
  {
    id: 4,
    company: "Textile Industry Client",
    location: "Tamil Nadu",
    testimonial: "60 years of the textile industry, third generation... JS Sir in particular paid close attention and offered some creative suggestions, both generally and in terms of work. JS Sir has proved his service in lighting design.",
    duration: "Ongoing collaboration",
    category: "Textile Manufacturing",
    logo: "/images/clients/textile-client-logo.png"
  }
]

export default function TestimonialsContent() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-slate-800">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center text-white"
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Client <span className="text-yellow-400">Testimonials</span>
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
                Discover what our clients say about our 19+ years of MEP engineering excellence
              </p>
            </motion.div>
          </ScrollReveal>
        </div>
      </section>

      {/* Featured Testimonials Carousel */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
                What Our Clients Say
              </h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                Real feedback from satisfied clients across various industries
              </p>
            </div>
          </ScrollReveal>
          
          <TestimonialCarousel testimonials={testimonials} />
        </div>
      </section>

      {/* Detailed Testimonials Grid */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
                Detailed Client Reviews
              </h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                In-depth testimonials showcasing our commitment to excellence
              </p>
            </div>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <ScrollReveal key={testimonial.id} delay={index * 0.1}>
                <TestimonialCard testimonial={testimonial} />
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-900 to-slate-800">
        <div className="container mx-auto px-4 text-center">
          <ScrollReveal>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-white"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Ready to Join Our Satisfied Clients?
              </h2>
              <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                Experience the same quality and commitment that has earned us these testimonials
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-yellow-400 text-slate-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-yellow-300 transition-colors"
              >
                Request Consultation
              </motion.button>
            </motion.div>
          </ScrollReveal>
        </div>
      </section>
    </div>
  )
}
